/* GamePort - Forest Theme (森林冒险 - 绿色系) */
/* 自然绿意风格，温馨森林探险氛围 */

:root {
  /* 主要颜色变量 - 绿色系 */
  --primary-color: #1B981B;
  --primary-hover: #136C13;
  --primary-light: #E2F3E2;
  --secondary-color: #3D5C3D;
  --accent-color: #2EAD2E;
  
  /* 背景颜色 */
  --bg-primary: #EDF7ED;
  --bg-secondary: #C9E8C9;
  --bg-tertiary: #E2F3E2;
  --bg-dark: #136C13;
  
  /* Header专用背景色 - 比主背景更深 */
  --header-bg-color: #136C13;
  
  /* 文字颜色 */
  --text-primary: #1B981B;
  --text-secondary: #3D5C3D;
  --text-tertiary: #1B981B;
  --text-light: #2EAD2E;
  --text-white: #ffffff;
  
  /* 辅助颜色变量 */
  --border-color: #3D5C3D;
  --border-hover: #1B981B;
  --shadow-color: rgba(27, 152, 27, 0.15);
  --shadow-hover: rgba(27, 152, 27, 0.25);
  
  /* 状态颜色 */
  --success-color: #1B981B;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --info-color: #0891b2;
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #1B981B;
  --btn-primary-hover: #136C13;
  --btn-primary-active: #0F5A0F;
  --btn-secondary-bg: #2EAD2E;
  --btn-secondary-hover: #1B981B;
  
  /* 链接颜色 */
  --link-color: #1B981B;
  --link-hover: #136C13;
  --link-visited: #3D5C3D;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #1B981B 0%, #136C13 100%);
  --gradient-secondary: linear-gradient(135deg, #EDF7ED 0%, #C9E8C9 100%);
}
